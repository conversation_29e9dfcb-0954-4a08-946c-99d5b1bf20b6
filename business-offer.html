<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Angebot zur Glas- und Rahmenreinigung</title>
    <style>
        @page {
            size: A4;
            margin: 15mm;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Arial', sans-serif;
            line-height: 1.5;
            color: #2c3e50;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            font-size: 14px;
        }

        .container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 0;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }

        .header {
            text-align: center;
            margin-bottom: 25px;
            padding: 25px 30px;
            background: linear-gradient(135deg, #1e4d8b 0%, #2980b9 100%);
            color: white;
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 15px solid transparent;
            border-right: 15px solid transparent;
            border-top: 10px solid #1e4d8b;
        }

        .logo-container {
            margin-bottom: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .logo {
            max-height: 80px;
            max-width: 200px;
            height: auto;
            width: auto;
            object-fit: contain;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }

        .title {
            color: white;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 0.5px;
        }

        .subtitle {
            color: rgba(255,255,255,0.9);
            font-size: 16px;
            font-weight: 400;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        .document-body {
            padding: 30px;
        }

        .section {
            margin-bottom: 28px;
            position: relative;
        }

        .section-title {
            color: #1e4d8b;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 15px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 5px solid #1e4d8b;
            border-radius: 0 8px 8px 0;
            box-shadow: 0 2px 8px rgba(30, 77, 139, 0.1);
            position: relative;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 16px;
        }

        .section-title::after {
            content: '';
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 4px;
            background: #1e4d8b;
            border-radius: 50%;
            box-shadow: 8px 0 0 #1e4d8b, 16px 0 0 #1e4d8b;
        }

        .greeting {
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            font-size: 15px;
            line-height: 1.6;
        }

        .content {
            text-align: justify;
            margin-bottom: 20px;
            line-height: 1.7;
            color: #34495e;
        }

        .services-list {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 20px;
            border-left: 5px solid #1e4d8b;
            border-radius: 0 12px 12px 0;
            margin: 20px 0;
            box-shadow: 0 4px 12px rgba(30, 77, 139, 0.08);
            position: relative;
        }

        .services-list::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #1e4d8b, #2980b9);
            clip-path: polygon(100% 0, 0 0, 100% 100%);
        }

        .services-list ul {
            list-style: none;
            padding-left: 0;
        }

        .services-list li {
            margin-bottom: 12px;
            padding-left: 25px;
            position: relative;
            color: #2c3e50;
            line-height: 1.6;
        }

        .services-list li:before {
            content: "✓";
            color: #1e4d8b;
            font-weight: bold;
            position: absolute;
            left: 0;
            top: 0;
            width: 18px;
            height: 18px;
            background: rgba(30, 77, 139, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .price-table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            box-shadow: 0 8px 25px rgba(30, 77, 139, 0.15);
            border-radius: 12px;
            overflow: hidden;
            background: white;
        }

        .price-table th {
            background: linear-gradient(135deg, #1e4d8b 0%, #2980b9 100%);
            color: white;
            padding: 16px 15px;
            text-align: left;
            font-weight: 700;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
        }

        .price-table th:first-child {
            border-radius: 12px 0 0 0;
        }

        .price-table th:last-child {
            border-radius: 0 12px 0 0;
            text-align: right;
        }

        .price-table td {
            padding: 14px 15px;
            border-bottom: 1px solid rgba(30, 77, 139, 0.1);
            transition: background-color 0.3s ease;
            font-size: 14px;
        }

        .price-table td:last-child {
            text-align: right;
            font-weight: 600;
        }

        .price-table tr:nth-child(even) {
            background: rgba(248, 249, 250, 0.7);
        }

        .price-table tr:hover {
            background: rgba(30, 77, 139, 0.05);
        }

        .price-table tr:last-child td {
            border-bottom: none;
        }

        .total-row {
            font-weight: 700 !important;
            background: linear-gradient(135deg, #e8f0fe 0%, #f0f7ff 100%) !important;
            border-top: 2px solid #1e4d8b;
        }

        .total-row:hover {
            background: linear-gradient(135deg, #e8f0fe 0%, #f0f7ff 100%) !important;
        }

        .final-total {
            background: linear-gradient(135deg, #1e4d8b 0%, #2980b9 100%) !important;
            color: white !important;
            font-weight: 700 !important;
            font-size: 16px !important;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        .final-total:hover {
            background: linear-gradient(135deg, #1e4d8b 0%, #2980b9 100%) !important;
        }

        .highlight-box {
            background: linear-gradient(135deg, #fff8e1 0%, #fffbf0 100%);
            border: 2px solid #ffc107;
            padding: 20px;
            margin: 25px 0;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
            position: relative;
        }

        .highlight-box::before {
            content: "⚠️";
            position: absolute;
            top: -12px;
            left: 20px;
            background: #ffc107;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .highlight-box strong {
            color: #b8860b;
            font-weight: 700;
        }

        .contact-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 25px;
            border-radius: 12px;
            border-left: 5px solid #1e4d8b;
            box-shadow: 0 4px 15px rgba(30, 77, 139, 0.1);
            position: relative;
        }

        .contact-info::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #1e4d8b, #2980b9);
            clip-path: polygon(100% 0, 0 0, 100% 100%);
        }

        .signature-block {
            margin-top: 35px;
        }

        .greeting-text {
            margin-bottom: 15px;
            color: #2c3e50;
            font-weight: 500;
        }

        .position-text {
            margin-bottom: 20px;
            color: #7f8c8d;
            font-style: italic;
        }

        .signature-name {
            font-weight: 700;
            color: #1e4d8b;
            font-size: 18px;
            margin-bottom: 5px;
            text-shadow: 0 1px 2px rgba(30, 77, 139, 0.1);
        }

        .company-details {
            margin-top: 15px;
            line-height: 1.8;
            font-size: 14px;
        }

        .company-details strong {
            color: #1e4d8b;
            font-size: 16px;
            display: block;
            margin-bottom: 10px;
        }

        .contact-icons {
            color: #1e4d8b;
            margin-right: 8px;
            font-size: 16px;
        }

        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                background: white !important;
            }

            .container {
                box-shadow: none !important;
                border-radius: 0 !important;
            }

            .header::after {
                display: none;
            }

            .section {
                page-break-inside: avoid;
            }

            .price-table {
                page-break-inside: avoid;
            }

            .highlight-box {
                page-break-inside: avoid;
            }

            .signature-block {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo-container">
                <img src="Logo.jpg" alt="S.U.Z Schutz und Sicherheit GmbH Logo" class="logo">
            </div>
            <div class="title">Angebot zur Glas- und Rahmenreinigung</div>
            <div class="subtitle">Kita Truchseßstraße 44, 53175 Bonn</div>
        </div>

        <div class="document-body">
            <!-- Greeting -->
            <div class="section">
                <div class="greeting">
                    Sehr geehrte Frau Dal,<br>
                    sehr geehrte Damen und Herren,
                </div>
            </div>

        <!-- Introduction -->
        <div class="section">
            <div class="content">
                vielen Dank für Ihre Anfrage zur Glas- und Rahmenreinigung in der Städtischen Kindertageseinrichtung, Truchseßstraße 44, 53175 Bonn. Gerne unterbreiten wir Ihnen auf Grundlage Ihrer Angaben folgendes Angebot:
            </div>
        </div>

        <!-- Services -->
        <div class="section">
            <div class="section-title">Leistungsumfang</div>
            <div class="services-list">
                <ul>
                    <li>Glas- und Rahmenreinigung aller angegebenen Flächen (ca. 120 m²)</li>
                    <li>Vorab-Besichtigung nach Terminvereinbarung mit Frau Blum unter Tel. 0228 77 87490</li>
                    <li>Durchführung gemäß Ihrer Vorgabe: keine nachträglichen Ansprüche bei Erschwernissen</li>
                </ul>
            </div>
        </div>

        <!-- Price Table -->
        <div class="section">
            <div class="section-title">Kostenübersicht</div>
            <table class="price-table">
                <thead>
                    <tr>
                        <th>Leistung</th>
                        <th>Menge</th>
                        <th>Einzelpreis</th>
                        <th>Gesamtpreis</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Glas- und Rahmenreinigung</td>
                        <td>120 m²</td>
                        <td>1,60 € / m²</td>
                        <td>192,00 €</td>
                    </tr>
                    <tr>
                        <td>Glasreinigung (geschätzt)</td>
                        <td>2 Stunden</td>
                        <td>50,00 € / Stunde</td>
                        <td>100,00 €</td>
                    </tr>
                    <tr>
                        <td>Anfahrt, Material, Arbeitsmittel</td>
                        <td>pauschal</td>
                        <td>–</td>
                        <td>100,00 €</td>
                    </tr>
                    <tr class="total-row">
                        <td colspan="3"><strong>Gesamtsumme netto</strong></td>
                        <td><strong>392,00 €</strong></td>
                    </tr>
                    <tr class="total-row">
                        <td colspan="3"><strong>zzgl. 19 % MwSt.</strong></td>
                        <td><strong>74,48 €</strong></td>
                    </tr>
                    <tr class="final-total">
                        <td colspan="3"><strong>Gesamtsumme brutto</strong></td>
                        <td><strong>466,48 €</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Important Notice -->
        <div class="section">
            <div class="section-title">Hinweis</div>
            <div class="highlight-box">
                Bitte senden Sie uns Ihre schriftliche Beauftragung oder Rückmeldung bis spätestens <strong>30.06.2025 – 12:00 Uhr</strong> an folgende E-Mail-Adresse:<br>
                <span class="contact-icons">📧</span> <strong><EMAIL></strong><br><br>
                Sollten Sie kein Interesse haben, bitten wir ebenfalls um eine kurze Rückmeldung.
            </div>
        </div>

        <!-- Signature -->
        <div class="section">
            <div class="signature-block">
                <div style="margin-bottom: 15px;">Mit freundlichen Grüßen</div>
                <div class="signature-name">Ali Mohamed</div>
                <div style="margin-bottom: 15px;">Geschäftsführer</div>
                
                <div class="contact-info">
                    <div class="company-details">
                        <strong>S.U.Z Schutz und Sicherheit GmbH</strong><br>
                        <span class="contact-icons">📞</span> 0176 23152477<br>
                        <span class="contact-icons">📧</span> <EMAIL><br>
                        <span class="contact-icons">🌐</span> www.suz-schutz-sicherheit.de
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
